# Agentic Framework with MCP Server

A comprehensive AI agent orchestration framework with Model Context Protocol (MCP) server implementation, based on Google ADK patterns for dynamic agent calling and routing.

## 🚀 Features

### Core Framework
- **Dynamic Agent Routing**: Automatically selects the best agent for each request
- **MCP Server Integration**: Full Model Context Protocol server implementation
- **Agent Registry**: Centralized agent management and discovery
- **Context Management**: Persistent conversation context and memory
- **REST API**: HTTP endpoints for easy integration
- **Web Interface**: Modern Bootstrap-based UI for testing and interaction

### Available Agents
1. **General Purpose Agent**: Routes requests to specialized agents
2. **Time Series Forecasting Agent**: Specialized in forecasting and predictions
3. **Software Bug Assistant Agent**: Debugging and code problem resolution
4. **Data Analysis Agent**: Data insights and statistical analysis
5. **Code Review Agent**: Code quality and optimization

### MCP Server Capabilities
- **Dynamic Agent Selection**: Routes requests to appropriate agents
- **Model Provider Integration**: Supports OpenAI, Anthropic, and local models
- **Context Persistence**: Maintains conversation history
- **Real-time Processing**: Asynchronous request handling
- **Error Handling**: Comprehensive error management and recovery

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   REST API      │    │   MCP Server    │
│   (Bootstrap)   │◄──►│   Controller    │◄──►│   (Socket)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Agent Registry │    │  Model Registry │
                       │                 │    │                 │
                       │ • Agent Discovery│    │ • Model Providers│
                       │ • Routing Logic │    │ • OpenAI/Anthropic│
                       │ • Priority Mgmt │    │ • Local Models  │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Specialized    │    │  Context        │
                       │  Agents         │    │  Manager        │
                       │                 │    │                 │
                       │ • Time Series   │    │ • Session Mgmt  │
                       │ • Bug Assistant │    │ • Memory Store  │
                       │ • Data Analysis │    │ • History       │
                       │ • Code Review   │    └─────────────────┘
                       └─────────────────┘
```

## 🛠️ Installation & Setup

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- Spring Boot 3.x

### Configuration
1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd agentic-framework
   ```

2. **Configure API Keys** in `src/main/resources/application.yml`:
   ```yaml
   mcp:
     models:
       openai:
         api-key: "your-openai-api-key"
         base-url: "https://api.openai.com/v1"
       anthropic:
         api-key: "your-anthropic-api-key"
         base-url: "https://api.anthropic.com/v1"
   ```

3. **Build the project**:
   ```bash
   mvn clean install
   ```

4. **Run the application**:
   ```bash
   mvn spring-boot:run
   ```

## 📡 API Endpoints

### Agent Management
- `GET /api/agents` - Get all available agents
- `GET /api/agents/{agentId}` - Get specific agent information
- `GET /api/agents/count` - Get agent count
- `POST /api/agents/refresh` - Refresh agent registry

### Agent Processing
- `POST /api/agents/process` - Process request with automatic agent selection
- `POST /api/agents/{agentId}/process` - Process request with specific agent
- `POST /api/agents/find` - Find agents that can handle a request

### Model Management
- `GET /api/models` - Get all available models
- `POST /api/models/process` - Process request with specific model

## 🔌 MCP Server Protocol

The MCP server supports the following request types:

### Request Types
- `agent_request` - Process request with automatic agent selection
- `get_agents` - Get list of available agents
- `model_request` - Process request with specific model
- `get_models` - Get list of available models
- `get_context` - Retrieve conversation context
- `clear_context` - Clear conversation context

### Example MCP Request
```json
{
  "type": "agent_request",
  "data": {
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["Can you help me forecast sales data?"],
    "modelName": "gpt-4o",
    "sessionId": "session-123"
  }
}
```

### Example MCP Response
```json
{
  "success": true,
  "data": {
    "responseId": "resp-456",
    "response": "🔮 **Time Series Forecasting Analysis**\n\nI can help you with time series forecasting...",
    "status": "SUCCESS"
  },
  "metadata": {
    "selected_agent_id": "time-series-forecasting-agent",
    "selected_agent_name": "Time Series Forecasting Agent",
    "agent_capabilities": {
      "time_series_analysis": "Analyze time series data for patterns and trends",
      "forecasting": "Generate predictions for future values"
    }
  }
}
```

## 🎯 Agent Capabilities

### Time Series Forecasting Agent
- **Keywords**: forecast, prediction, time series, trend, future
- **Capabilities**: 
  - Time series analysis and forecasting
  - Seasonal pattern detection
  - Statistical modeling
  - Anomaly detection

### Software Bug Assistant Agent
- **Keywords**: bug, error, exception, debug, fix, issue
- **Capabilities**:
  - Error message analysis
  - Stack trace interpretation
  - Code debugging assistance
  - Fix suggestions

### Data Analysis Agent
- **Keywords**: analyze, data, statistics, chart, graph, insights
- **Capabilities**:
  - Data analysis and insights
  - Statistical analysis
  - Data visualization
  - Pattern recognition

### Code Review Agent
- **Keywords**: code review, code quality, refactor, optimize
- **Capabilities**:
  - Code quality assessment
  - Performance optimization
  - Best practices identification
  - Security analysis

## 🌐 Web Interface

Access the web interface at `http://localhost:8080` to:

- **View Available Agents**: See all registered agents with their capabilities
- **Select Specific Agents**: Choose a particular agent for your request
- **Chat Interface**: Interactive chat with automatic agent routing
- **Quick Actions**: Pre-defined buttons for common tasks
- **Real-time Processing**: Live agent selection and response display

## 🔧 Development

### Adding New Agents

1. **Create Agent Class**:
   ```java
   @Component
   public class MyCustomAgent implements Agent {
       @Override
       public String getAgentId() { return "my-custom-agent"; }
       
       @Override
       public boolean canHandle(ModelRequest request) {
           // Implement your logic to determine if this agent can handle the request
           return request.getPrompt().contains("your-keyword");
       }
       
       @Override
       public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
           // Implement your agent's processing logic
       }
   }
   ```

2. **Register in AgentRegistry**:
   ```java
   private void registerDefaultAgents() {
       registerAgent(new MyCustomAgent());
   }
   ```

### Customizing Agent Routing

Modify the `canHandle` method in agents to implement custom routing logic:

```java
@Override
public boolean canHandle(ModelRequest request) {
    String prompt = getPromptFromRequest(request).toLowerCase();
    
    // Add your custom keywords and logic
    return prompt.contains("your-keyword") || 
           prompt.contains("another-keyword");
}
```

## 🧪 Testing

### Test the Framework

1. **Start the application**:
   ```bash
   mvn spring-boot:run
   ```

2. **Access the web interface**:
   ```
   http://localhost:8080
   ```

3. **Test with curl**:
   ```bash
   # Get all agents
   curl http://localhost:8080/api/agents
   
   # Process a request
   curl -X POST http://localhost:8080/api/agents/process \
     -H "Content-Type: application/json" \
     -d '{
       "systemMessage": "You are a helpful assistant.",
       "userMessages": ["Can you forecast sales data?"],
       "modelName": "gpt-4o"
     }'
   ```

### Test MCP Server

Connect to the MCP server on port 8081:

```bash
# Using netcat for testing
echo '{"type":"get_agents","data":{}}' | nc localhost 8081
```

## 📊 Monitoring

### Health Checks
- `GET /actuator/health` - Application health status
- `GET /actuator/metrics` - Application metrics

### Logging
Logs are written to `logs/agentic-framework.log` with configurable levels.

## 🔒 Security

- **API Key Management**: Secure storage of model provider API keys
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Secure error responses without sensitive information
- **CORS Configuration**: Configurable cross-origin resource sharing

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t agentic-framework .

# Run container
docker run -p 8080:8080 -p 8081:8081 agentic-framework
```

### Production Configuration
1. Set appropriate logging levels
2. Configure external databases for context storage
3. Set up monitoring and alerting
4. Configure SSL/TLS for secure communication

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Based on Google ADK patterns for agent development
- Inspired by Model Context Protocol specifications
- Built with Spring Boot and modern Java technologies
