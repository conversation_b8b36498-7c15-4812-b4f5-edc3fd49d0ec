1.https://github.com/google/adk-samples/blob/main/java/agents/time-series-forecasting/src/main/java/com/google/adk/samples/agents/timeseriesforecasting/ForecastingAgent.java
2.https://github.com/google/adk-samples/blob/main/java/agents/software-bug-assistant/software-bug-assistant/src/main/java/SoftwareBugAssistant.java
3.https://github.com/google/adk-samples
4.Take above references and come up with a plan for implementing agentic framework with MCP server and tools in mcp server i want actual mcp server implmentation.
5.Take google adk as reference and dont write code as per the git lab use cases i provided i want you to come up with mcp service and dynamicalling calling any agent and getting response.