<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Framework - AI Agent Orchestration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .agent-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
        }
        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }
        .agent-message {
            background-color: #e9ecef;
            color: #212529;
        }
        .agent-badge {
            font-size: 0.8em;
            padding: 2px 8px;
            border-radius: 12px;
            background-color: #28a745;
            color: white;
        }
        .loading-spinner {
            display: none;
        }
        .capability-tag {
            display: inline-block;
            background-color: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            margin: 2px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark gradient-bg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                Agentic Framework
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-circle text-success me-1"></i>
                    <span id="status-indicator">Connected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Agent Selection Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Available Agents
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="agents-list">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading agents...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Details -->
                <div class="card mt-3" id="agent-details" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Agent Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <h6 id="selected-agent-name"></h6>
                        <p class="text-muted" id="selected-agent-description"></p>
                        <div id="agent-capabilities"></div>
                    </div>
                </div>
            </div>

            <!-- Chat Interface -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Agent Chat
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chat-container" id="chat-container">
                            <div class="message agent-message">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="agent-badge">System</span>
                                </div>
                                <p class="mb-0">Hello! I'm the Agentic Framework. I can help you with various tasks by routing your requests to specialized agents. What would you like to do today?</p>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <form id="chat-form">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="message-input" 
                                           placeholder="Type your message here..." required>
                                    <button class="btn btn-primary" type="submit" id="send-button">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </form>
                            
                            <div class="loading-spinner text-center mt-3" id="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Processing...</span>
                                </div>
                                <p class="mt-2">Processing with AI agent...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="sendQuickMessage('Can you help me forecast sales data for the next quarter?')">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Time Series Forecast
                                </button>
                                <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="sendQuickMessage('I have a bug in my code that causes a null pointer exception')">
                                    <i class="fas fa-bug me-1"></i>
                                    Debug Code Issue
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="sendQuickMessage('Can you analyze this dataset and provide insights?')">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    Data Analysis
                                </button>
                                <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="sendQuickMessage('Please review this code for best practices and optimization')">
                                    <i class="fas fa-code me-1"></i>
                                    Code Review
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedAgentId = null;
        let agents = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadAgents();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('chat-form').addEventListener('submit', function(e) {
                e.preventDefault();
                sendMessage();
            });
        }

        async function loadAgents() {
            try {
                const response = await fetch('/api/agents');
                if (response.ok) {
                    agents = await response.json();
                    displayAgents();
                } else {
                    showError('Failed to load agents');
                }
            } catch (error) {
                console.error('Error loading agents:', error);
                showError('Error loading agents');
            }
        }

        function displayAgents() {
            const agentsList = document.getElementById('agents-list');
            agentsList.innerHTML = '';

            agents.forEach(agent => {
                const agentCard = document.createElement('div');
                agentCard.className = 'card agent-card mb-2';
                agentCard.onclick = () => selectAgent(agent.agentId);
                
                agentCard.innerHTML = `
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title mb-1">${agent.name}</h6>
                                <p class="card-text text-muted small mb-2">${agent.description}</p>
                                <div class="d-flex flex-wrap">
                                    ${Object.entries(agent.capabilities).slice(0, 2).map(([key, value]) => 
                                        `<span class="capability-tag">${key}</span>`
                                    ).join('')}
                                </div>
                            </div>
                            <span class="badge bg-secondary">Priority: ${agent.priority}</span>
                        </div>
                    </div>
                `;
                
                agentsList.appendChild(agentCard);
            });
        }

        function selectAgent(agentId) {
            selectedAgentId = agentId;
            const agent = agents.find(a => a.agentId === agentId);
            
            if (agent) {
                document.getElementById('agent-details').style.display = 'block';
                document.getElementById('selected-agent-name').textContent = agent.name;
                document.getElementById('selected-agent-description').textContent = agent.description;
                
                const capabilitiesDiv = document.getElementById('agent-capabilities');
                capabilitiesDiv.innerHTML = Object.entries(agent.capabilities).map(([key, value]) => 
                    `<span class="capability-tag" title="${value}">${key}</span>`
                ).join('');
            }
        }

        function sendQuickMessage(message) {
            document.getElementById('message-input').value = message;
            sendMessage();
        }

        async function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessageToChat('user', message);
            messageInput.value = '';

            // Show loading spinner
            document.getElementById('loading-spinner').style.display = 'block';
            document.getElementById('send-button').disabled = true;

            try {
                const request = {
                    systemMessage: "You are a helpful AI assistant.",
                    userMessages: [message],
                    modelName: "gpt-4o",
                    sessionId: generateSessionId()
                };

                let response;
                if (selectedAgentId) {
                    // Use specific agent
                    response = await fetch(`/api/agents/${selectedAgentId}/process`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(request)
                    });
                } else {
                    // Use automatic agent selection
                    response = await fetch('/api/agents/process', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(request)
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const agentName = result.selectedAgentName || 'AI Assistant';
                        addMessageToChat('agent', result.modelResponse.response, agentName);
                    } else {
                        addMessageToChat('agent', `Error: ${result.error}`, 'System');
                    }
                } else {
                    addMessageToChat('agent', 'Error: Failed to process request', 'System');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                addMessageToChat('agent', 'Error: Network error occurred', 'System');
            } finally {
                // Hide loading spinner
                document.getElementById('loading-spinner').style.display = 'none';
                document.getElementById('send-button').disabled = false;
            }
        }

        function addMessageToChat(sender, message, agentName = null) {
            const chatContainer = document.getElementById('chat-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'agent') {
                messageDiv.innerHTML = `
                    <div class="d-flex align-items-center mb-1">
                        <span class="agent-badge">${agentName || 'AI Assistant'}</span>
                    </div>
                    <div class="message-content">${formatMessage(message)}</div>
                `;
            } else {
                messageDiv.innerHTML = `<div class="message-content">${formatMessage(message)}</div>`;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function formatMessage(message) {
            // Convert markdown-like formatting to HTML
            return message
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>');
        }

        function generateSessionId() {
            return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        }

        function showError(message) {
            addMessageToChat('agent', `Error: ${message}`, 'System');
        }
    </script>
</body>
</html>
