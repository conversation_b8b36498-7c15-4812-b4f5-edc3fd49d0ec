package com.agentic.framework.mcp;

import com.agentic.framework.agent.Agent;
import com.agentic.framework.agent.AgentRegistry;
import com.agentic.framework.config.McpServerConfig;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Enhanced MCP Server with Agent Integration
 * Provides dynamic agent calling and routing capabilities
 * Based on Google ADK patterns for agent orchestration
 */
@Component
public class AgenticMcpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(AgenticMcpServer.class);
    
    private final McpServerConfig config;
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final AgentRegistry agentRegistry;
    private final ObjectMapper objectMapper;
    
    private ServerSocket serverSocket;
    private ExecutorService executorService;
    private AtomicBoolean running = new AtomicBoolean(false);
    
    @Autowired
    public AgenticMcpServer(McpServerConfig config, ModelRegistry modelRegistry, 
                           ContextManager contextManager, AgentRegistry agentRegistry) {
        this.config = config;
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.agentRegistry = agentRegistry;
        this.objectMapper = new ObjectMapper();
    }
    
    @PostConstruct
    public void start() {
        try {
            initializeServer();
            startServer();
            int actualPort = serverSocket.getLocalPort();
            logger.info("Agentic MCP Server started successfully on {}:{}", config.getHost(), actualPort);
            logger.info("Available agents: {}", agentRegistry.getAgentCount());
        } catch (IOException e) {
            logger.error("Failed to start Agentic MCP Server", e);
            throw new RuntimeException("Failed to start Agentic MCP Server", e);
        }
    }
    
    @PreDestroy
    public void stop() {
        shutdown();
    }
    
    private void initializeServer() throws IOException {
        serverSocket = new ServerSocket(config.getPort());
        int actualPort = serverSocket.getLocalPort();
        if (config.getPort() == 0) {
            logger.info("Agentic MCP Server bound to random port: {}", actualPort);
        }
        executorService = Executors.newFixedThreadPool(config.getMaxConnections());
        running.set(true);
    }
    
    private void startServer() {
        new Thread(() -> {
            while (running.get() && !serverSocket.isClosed()) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    clientSocket.setSoTimeout(config.getReadTimeout());
                    
                    if (running.get()) {
                        AgenticClientHandler clientHandler = new AgenticClientHandler(
                            clientSocket, modelRegistry, contextManager, agentRegistry, objectMapper);
                        executorService.submit(clientHandler);
                        logger.debug("New client connection accepted: {}", clientSocket.getInetAddress());
                    } else {
                        clientSocket.close();
                    }
                } catch (IOException e) {
                    if (running.get()) {
                        logger.error("Error accepting client connection", e);
                    }
                }
            }
        }, "Agentic-MCP-Server-Acceptor").start();
    }
    
    public void shutdown() {
        logger.info("Shutting down Agentic MCP Server...");
        running.set(false);
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        if (serverSocket != null && !serverSocket.isClosed()) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                logger.error("Error closing server socket", e);
            }
        }
        
        logger.info("Agentic MCP Server shutdown complete");
    }
    
    public boolean isRunning() {
        return running.get();
    }
    
    public int getActiveConnections() {
        return executorService != null ? ((java.util.concurrent.ThreadPoolExecutor) executorService).getActiveCount() : 0;
    }
    
    /**
     * Enhanced Client Handler with Agent Integration
     */
    private static class AgenticClientHandler implements Runnable {
        
        private final Socket clientSocket;
        private final ModelRegistry modelRegistry;
        private final ContextManager contextManager;
        private final AgentRegistry agentRegistry;
        private final ObjectMapper objectMapper;
        private final String clientId;
        
        public AgenticClientHandler(Socket clientSocket, ModelRegistry modelRegistry, 
                                  ContextManager contextManager, AgentRegistry agentRegistry, 
                                  ObjectMapper objectMapper) {
            this.clientSocket = clientSocket;
            this.modelRegistry = modelRegistry;
            this.contextManager = contextManager;
            this.agentRegistry = agentRegistry;
            this.objectMapper = objectMapper;
            this.clientId = UUID.randomUUID().toString();
        }
        
        @Override
        public void run() {
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(clientSocket.getInputStream()));
                 java.io.PrintWriter writer = new java.io.PrintWriter(clientSocket.getOutputStream(), true)) {
                
                logger.info("Agentic client handler started for client: {}", clientId);
                
                String inputLine;
                while ((inputLine = reader.readLine()) != null) {
                    try {
                        // Parse the incoming request
                        McpRequest request = objectMapper.readValue(inputLine, McpRequest.class);
                        
                        // Process the request with agent integration
                        McpResponse response = processRequestWithAgents(request);
                        
                        // Send response back to client
                        String responseJson = objectMapper.writeValueAsString(response);
                        writer.println(responseJson);
                        
                    } catch (Exception e) {
                        logger.error("Error processing client request", e);
                        
                        // Send error response
                        McpResponse errorResponse = new McpResponse();
                        errorResponse.setSuccess(false);
                        errorResponse.setError("Error processing request: " + e.getMessage());
                        
                        String errorJson = objectMapper.writeValueAsString(errorResponse);
                        writer.println(errorJson);
                    }
                }
                
            } catch (IOException e) {
                logger.error("Error in agentic client handler for client: {}", clientId, e);
            } finally {
                try {
                    clientSocket.close();
                    logger.info("Client connection closed for client: {}", clientId);
                } catch (IOException e) {
                    logger.error("Error closing client socket", e);
                }
            }
        }
        
        private McpResponse processRequestWithAgents(McpRequest request) {
            McpResponse response = new McpResponse();
            
            try {
                switch (request.getType()) {
                    case "agent_request":
                        response = handleAgentRequest(request);
                        break;
                    case "get_agents":
                        response = handleGetAgents(request);
                        break;
                    case "model_request":
                        response = handleModelRequest(request);
                        break;
                    case "get_models":
                        response = handleGetModels(request);
                        break;
                    case "get_context":
                        response = handleGetContext(request);
                        break;
                    case "clear_context":
                        response = handleClearContext(request);
                        break;
                    default:
                        response.setSuccess(false);
                        response.setError("Unknown request type: " + request.getType());
                }
            } catch (Exception e) {
                logger.error("Error processing request type: {}", request.getType(), e);
                response.setSuccess(false);
                response.setError("Internal error: " + e.getMessage());
            }
            
            return response;
        }
        
        private McpResponse handleAgentRequest(McpRequest request) {
            McpResponse response = new McpResponse();
            
            try {
                // Extract model request data
                ModelRequest modelRequest = objectMapper.convertValue(request.getData(), ModelRequest.class);
                
                // Generate session ID if not provided
                if (modelRequest.getSessionId() == null) {
                    modelRequest.setSessionId(UUID.randomUUID().toString());
                }
                
                // Find the best agent for this request
                Agent bestAgent = agentRegistry.getBestAgentForRequest(modelRequest);
                
                if (bestAgent != null) {
                    logger.info("Routing request to agent: {} ({})", bestAgent.getAgentName(), bestAgent.getAgentId());
                    
                    // Process request with the selected agent
                    CompletableFuture<ModelResponse> futureResponse = bestAgent.processRequest(modelRequest);
                    ModelResponse modelResponse = futureResponse.get();
                    
                    // Build response with agent information
                    response.setSuccess(true);
                    response.setData(modelResponse);
                    
                    // Add agent metadata
                    java.util.Map<String, Object> metadata = new java.util.HashMap<>();
                    metadata.put("selected_agent_id", bestAgent.getAgentId());
                    metadata.put("selected_agent_name", bestAgent.getAgentName());
                    metadata.put("agent_capabilities", bestAgent.getCapabilities());
                    response.setMetadata(metadata);
                    
                } else {
                    logger.warn("No suitable agent found for request");
                    response.setSuccess(false);
                    response.setError("No suitable agent found for this request");
                }
                
            } catch (Exception e) {
                logger.error("Error handling agent request", e);
                response.setSuccess(false);
                response.setError("Agent request failed: " + e.getMessage());
            }
            
            return response;
        }
        
        private McpResponse handleGetAgents(McpRequest request) {
            McpResponse response = new McpResponse();
            
            try {
                var agentInfo = agentRegistry.getAgentInfo();
                response.setSuccess(true);
                response.setData(agentInfo);
                
            } catch (Exception e) {
                logger.error("Error getting agents", e);
                response.setSuccess(false);
                response.setError("Failed to get agents: " + e.getMessage());
            }
            
            return response;
        }
        
        private McpResponse handleModelRequest(McpRequest request) {
            McpResponse response = new McpResponse();
            
            try {
                // Extract model request data
                ModelRequest modelRequest = objectMapper.convertValue(request.getData(), ModelRequest.class);
                
                // Generate session ID if not provided
                if (modelRequest.getSessionId() == null) {
                    modelRequest.setSessionId(UUID.randomUUID().toString());
                }
                
                // Get model provider and process request
                var modelProvider = modelRegistry.getModelProvider(modelRequest.getModelName());
                CompletableFuture<ModelResponse> futureResponse = modelProvider.processRequest(modelRequest);
                
                // Wait for response
                ModelResponse modelResponse = futureResponse.get();
                
                // Build response
                response.setSuccess(true);
                response.setData(modelResponse);
                
            } catch (Exception e) {
                logger.error("Error handling model request", e);
                response.setSuccess(false);
                response.setError("Model request failed: " + e.getMessage());
            }
            
            return response;
        }
        
        private McpResponse handleGetModels(McpRequest request) {
            McpResponse response = new McpResponse();
            
            try {
                var models = modelRegistry.getAllModels();
                response.setSuccess(true);
                response.setData(models);
                
            } catch (Exception e) {
                logger.error("Error getting models", e);
                response.setSuccess(false);
                response.setError("Failed to get models: " + e.getMessage());
            }
            
            return response;
        }
        
        private McpResponse handleGetContext(McpRequest request) {
            McpResponse response = new McpResponse();

            try {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> data = (java.util.Map<String, Object>) request.getData();
                String userId = (String) data.get("userId");
                String sessionId = (String) data.get("sessionId");
                Integer limit = (Integer) data.getOrDefault("limit", 10);

                var context = contextManager.getContext(userId, sessionId, limit);
                response.setSuccess(true);
                response.setData(context);

            } catch (Exception e) {
                logger.error("Error getting context", e);
                response.setSuccess(false);
                response.setError("Failed to get context: " + e.getMessage());
            }

            return response;
        }
        
        private McpResponse handleClearContext(McpRequest request) {
            McpResponse response = new McpResponse();

            try {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> data = (java.util.Map<String, Object>) request.getData();
                String userId = (String) data.get("userId");
                String sessionId = (String) data.get("sessionId");

                if (sessionId != null) {
                    contextManager.clearContext(userId, sessionId);
                } else {
                    contextManager.clearUserData(userId);
                }

                response.setSuccess(true);
                response.setData("Context cleared successfully");

            } catch (Exception e) {
                logger.error("Error clearing context", e);
                response.setSuccess(false);
                response.setError("Failed to clear context: " + e.getMessage());
            }

            return response;
        }
        
        // Inner classes for MCP protocol
        private static class McpRequest {
            private String type;
            private Object data;
            
            // Getters and setters
            public String getType() { return type; }
            public void setType(String type) { this.type = type; }
            public Object getData() { return data; }
            public void setData(Object data) { this.data = data; }
        }
        
        private static class McpResponse {
            private boolean success;
            private Object data;
            private String error;
            private java.util.Map<String, Object> metadata;
            
            // Getters and setters
            public boolean isSuccess() { return success; }
            public void setSuccess(boolean success) { this.success = success; }
            public Object getData() { return data; }
            public void setData(Object data) { this.data = data; }
            public String getError() { return error; }
            public void setError(String error) { this.error = error; }
            public java.util.Map<String, Object> getMetadata() { return metadata; }
            public void setMetadata(java.util.Map<String, Object> metadata) { this.metadata = metadata; }
        }
    }
}
