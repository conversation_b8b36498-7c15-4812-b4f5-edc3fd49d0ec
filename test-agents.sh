#!/bin/bash

# Agentic Framework Test Script
# This script demonstrates the functionality of the agent framework

BASE_URL="http://localhost:8080"
MCP_HOST="localhost"
MCP_PORT="8081"

echo "🤖 Agentic Framework Test Script"
echo "================================="
echo ""

# Function to check if server is running
check_server() {
    echo "🔍 Checking if server is running..."
    if curl -s "$BASE_URL/api/agents" > /dev/null 2>&1; then
        echo "✅ Server is running at $BASE_URL"
        return 0
    else
        echo "❌ Server is not running. Please start the application first."
        echo "   Run: mvn spring-boot:run"
        return 1
    fi
}

# Function to test agent listing
test_get_agents() {
    echo ""
    echo "📋 Testing: Get All Agents"
    echo "-------------------------"
    response=$(curl -s "$BASE_URL/api/agents")
    echo "Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to test agent count
test_agent_count() {
    echo ""
    echo "🔢 Testing: Get Agent Count"
    echo "---------------------------"
    response=$(curl -s "$BASE_URL/api/agents/count")
    echo "Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to test automatic agent selection
test_auto_agent_selection() {
    echo ""
    echo "🎯 Testing: Automatic Agent Selection"
    echo "-----------------------------------"
    
    # Test time series forecasting
    echo "Testing Time Series Forecasting Agent..."
    response=$(curl -s -X POST "$BASE_URL/api/agents/process" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["Can you help me forecast sales data for the next quarter?"],
            "modelName": "gpt-4o"
        }')
    echo "Time Series Request Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    echo ""
    echo "Testing Software Bug Assistant Agent..."
    response=$(curl -s -X POST "$BASE_URL/api/agents/process" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["I have a bug in my code that causes a null pointer exception"],
            "modelName": "gpt-4o"
        }')
    echo "Bug Assistant Request Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    echo ""
    echo "Testing Data Analysis Agent..."
    response=$(curl -s -X POST "$BASE_URL/api/agents/process" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["Can you analyze this dataset and provide insights?"],
            "modelName": "gpt-4o"
        }')
    echo "Data Analysis Request Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    echo ""
    echo "Testing Code Review Agent..."
    response=$(curl -s -X POST "$BASE_URL/api/agents/process" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["Please review this code for best practices and optimization"],
            "modelName": "gpt-4o"
        }')
    echo "Code Review Request Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to test specific agent selection
test_specific_agent() {
    echo ""
    echo "🎯 Testing: Specific Agent Selection"
    echo "-----------------------------------"
    
    echo "Testing Time Series Forecasting Agent directly..."
    response=$(curl -s -X POST "$BASE_URL/api/agents/time-series-forecasting-agent/process" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["I need to forecast monthly sales data"],
            "modelName": "gpt-4o"
        }')
    echo "Direct Time Series Agent Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to test agent discovery
test_agent_discovery() {
    echo ""
    echo "🔍 Testing: Agent Discovery"
    echo "---------------------------"
    
    response=$(curl -s -X POST "$BASE_URL/api/agents/find" \
        -H "Content-Type: application/json" \
        -d '{
            "systemMessage": "You are a helpful AI assistant.",
            "userMessages": ["I need help with forecasting"],
            "modelName": "gpt-4o"
        }')
    echo "Agents that can handle forecasting request:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to test MCP server
test_mcp_server() {
    echo ""
    echo "🔌 Testing: MCP Server"
    echo "---------------------"
    
    # Check if MCP server is running
    if nc -z "$MCP_HOST" "$MCP_PORT" 2>/dev/null; then
        echo "✅ MCP Server is running on $MCP_HOST:$MCP_PORT"
        
        echo "Testing MCP get_agents request..."
        echo '{"type":"get_agents","data":{}}' | nc "$MCP_HOST" "$MCP_PORT" 2>/dev/null || echo "Failed to connect to MCP server"
    else
        echo "❌ MCP Server is not running on $MCP_HOST:$MCP_PORT"
    fi
}

# Function to test web interface
test_web_interface() {
    echo ""
    echo "🌐 Testing: Web Interface"
    echo "------------------------"
    
    if curl -s "$BASE_URL" > /dev/null 2>&1; then
        echo "✅ Web interface is accessible at $BASE_URL"
        echo "   Open your browser and navigate to: $BASE_URL"
    else
        echo "❌ Web interface is not accessible"
    fi
}

# Function to show usage examples
show_examples() {
    echo ""
    echo "📚 Usage Examples"
    echo "================="
    echo ""
    echo "1. Get all agents:"
    echo "   curl $BASE_URL/api/agents"
    echo ""
    echo "2. Process request with automatic agent selection:"
    echo "   curl -X POST $BASE_URL/api/agents/process \\"
    echo "     -H \"Content-Type: application/json\" \\"
    echo "     -d '{\"systemMessage\":\"You are helpful\",\"userMessages\":[\"Help me forecast data\"],\"modelName\":\"gpt-4o\"}'"
    echo ""
    echo "3. Use specific agent:"
    echo "   curl -X POST $BASE_URL/api/agents/time-series-forecasting-agent/process \\"
    echo "     -H \"Content-Type: application/json\" \\"
    echo "     -d '{\"systemMessage\":\"You are helpful\",\"userMessages\":[\"Forecast sales\"],\"modelName\":\"gpt-4o\"}'"
    echo ""
    echo "4. Find agents for a request:"
    echo "   curl -X POST $BASE_URL/api/agents/find \\"
    echo "     -H \"Content-Type: application/json\" \\"
    echo "     -d '{\"systemMessage\":\"You are helpful\",\"userMessages\":[\"Debug my code\"],\"modelName\":\"gpt-4o\"}'"
    echo ""
}

# Main execution
main() {
    echo "Starting Agentic Framework tests..."
    echo ""
    
    # Check if server is running
    if ! check_server; then
        exit 1
    fi
    
    # Run all tests
    test_get_agents
    test_agent_count
    test_auto_agent_selection
    test_specific_agent
    test_agent_discovery
    test_mcp_server
    test_web_interface
    show_examples
    
    echo ""
    echo "✅ All tests completed!"
    echo ""
    echo "🎉 The Agentic Framework is working correctly!"
    echo "   - REST API: $BASE_URL"
    echo "   - Web Interface: $BASE_URL"
    echo "   - MCP Server: $MCP_HOST:$MCP_PORT"
    echo ""
    echo "💡 Try the web interface for an interactive experience!"
}

# Run main function
main
